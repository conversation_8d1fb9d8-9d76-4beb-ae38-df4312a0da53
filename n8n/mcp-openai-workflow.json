{"name": "MCP工具8阶段分析工作流-OpenAI版", "nodes": [{"parameters": {"httpMethod": "POST", "path": "mcp-analysis", "responseMode": "responseNode", "options": {}}, "id": "webhook-start", "name": "接收MCP代码", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "webhookId": "mcp-analysis-webhook"}, {"parameters": {"values": {"string": [{"name": "sourceCode", "value": "={{ $json.body.code || $json.body.sourceCode }}"}, {"name": "projectName", "value": "={{ $json.body.projectName || 'MCP工具项目' }}"}]}}, "id": "init-data", "name": "初始化数据", "type": "n8n-nodes-base.set", "typeVersion": 3, "position": [460, 300]}, {"parameters": {"resource": "chat", "model": "gpt-4o", "options": {"temperature": 0.1, "maxTokens": 4000}, "messages": {"messageValues": [{"role": "system", "content": "你是世界级的MCP工具分析专家，专门负责代码理解和结构分析。你具备深厚的编程语言知识和MCP协议理解能力。"}, {"role": "user", "content": "请分析以下MCP工具源代码，提取核心结构信息：\n\n代码内容：\n{{ $json.sourceCode }}\n\n请按照以下格式输出分析结果：\n```json\n{\n  \"service_type\": \"single_tool|multi_tool\",\n  \"tool_count\": 数字,\n  \"tools_overview\": [\n    {\n      \"name\": \"工具英文名\",\n      \"chinese_name\": \"生成的中文名\",\n      \"core_function\": \"一句话核心功能\",\n      \"complexity_level\": \"simple|medium|complex\"\n    }\n  ],\n  \"technical_stack\": \"使用的技术栈\",\n  \"architecture_pattern\": \"架构模式\"\n}\n```"}]}}, "id": "stage1-analysis", "name": "阶段1：代码理解与结构分析", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [680, 200]}, {"parameters": {"jsCode": "// 解析OpenAI API响应并提取JSON内容\nconst response = $input.item(0).json;\nlet content = '';\n\nif (response.choices && response.choices[0] && response.choices[0].message) {\n  content = response.choices[0].message.content;\n} else if (response.message && response.message.content) {\n  content = response.message.content;\n} else {\n  content = JSON.stringify(response);\n}\n\n// 尝试提取JSON部分\nlet jsonMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\nif (jsonMatch) {\n  try {\n    const parsedJson = JSON.parse(jsonMatch[1]);\n    return { response: parsedJson, raw_content: content };\n  } catch (e) {\n    return { response: content, raw_content: content, parse_error: e.message };\n  }\n} else {\n  return { response: content, raw_content: content };\n}"}, "id": "parse-stage1", "name": "解析阶段1结果", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 200]}, {"parameters": {"resource": "chat", "model": "gpt-4o", "options": {"temperature": 0.1, "maxTokens": 4000}, "messages": {"messageValues": [{"role": "system", "content": "你是JSON Schema专家和API设计师，专门负责分析和构建标准化的输入参数模式。"}, {"role": "user", "content": "基于阶段1的分析结果和原始代码，为每个工具生成精确的JSON Schema格式的inputSchema：\n\n阶段1结果：\n{{ JSON.stringify($('parse-stage1').item.json.response) }}\n\n原始代码：\n{{ $('init-data').item.json.sourceCode }}\n\n请为每个工具生成完整的inputSchema，格式如下：\n```json\n{\n  \"input_schemas\": [\n    {\n      \"tool_name\": \"工具名\",\n      \"schema\": {\n        \"type\": \"object\",\n        \"required\": [\"必需参数数组\"],\n        \"properties\": {\n          \"参数名\": {\n            \"type\": \"数据类型\",\n            \"description\": \"清晰的中文描述\"\n          }\n        },\n        \"additionalProperties\": false\n      }\n    }\n  ]\n}\n```"}]}}, "id": "stage2-input-schema", "name": "阶段2：输入参数模式提取", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1120, 200]}, {"parameters": {"jsCode": "// 解析阶段2结果\nconst response = $input.item(0).json;\nlet content = '';\n\nif (response.choices && response.choices[0] && response.choices[0].message) {\n  content = response.choices[0].message.content;\n} else if (response.message && response.message.content) {\n  content = response.message.content;\n} else {\n  content = JSON.stringify(response);\n}\n\nlet jsonMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\nif (jsonMatch) {\n  try {\n    const parsedJson = JSON.parse(jsonMatch[1]);\n    return { response: parsedJson, raw_content: content };\n  } catch (e) {\n    return { response: content, raw_content: content, parse_error: e.message };\n  }\n} else {\n  return { response: content, raw_content: content };\n}"}, "id": "parse-stage2", "name": "解析阶段2结果", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 200]}, {"parameters": {"jsCode": "// 整合所有阶段结果并生成最终JSON配置\nconst stage1 = $('parse-stage1').item.json.response;\nconst stage2 = $('parse-stage2').item.json.response;\nconst sourceCode = $('init-data').item.json.sourceCode;\nconst projectName = $('init-data').item.json.projectName;\n\n// 生成最终配置\nconst finalConfig = [];\n\nif (stage1.tools_overview && Array.isArray(stage1.tools_overview)) {\n  stage1.tools_overview.forEach((tool, index) => {\n    // 查找对应的inputSchema\n    let inputSchema = { type: \"object\", properties: {}, required: [] };\n    if (stage2.input_schemas && Array.isArray(stage2.input_schemas)) {\n      const schemaMatch = stage2.input_schemas.find(s => s.tool_name === tool.name);\n      if (schemaMatch && schemaMatch.schema) {\n        inputSchema = schemaMatch.schema;\n      }\n    }\n\n    const config = {\n      \"ID\": null,\n      \"c_name\": tool.chinese_name || tool.name,\n      \"description\": `${tool.core_function} - ${tool.name}`,\n      \"descriptionChinese\": `${tool.core_function} - ${tool.chinese_name || tool.name}`,\n      \"fullName\": `${projectName}--${tool.name}`,\n      \"inputSchema\": inputSchema,\n      \"is_single_call\": 1,\n      \"keywords\": `${tool.name},${tool.chinese_name || ''},MCP工具,${tool.core_function}`,\n      \"multiFileType\": 0,\n      \"name\": tool.name,\n      \"outputSchema\": { \"type\": \"object\" },\n      \"platform\": \"all\",\n      \"points\": 2,\n      \"projectId\": null,\n      \"projectUUId\": \"mcp-tools-project\",\n      \"regex\": null,\n      \"supportedExtensions\": [],\n      \"canProcessDirectory\": false,\n      \"canDirectExecute\": tool.complexity_level !== \"complex\",\n      \"isDangerous\": false,\n      \"canRunIndependently\": true,\n      \"prerequisiteTools\": [],\n      \"shouldExclude\": false,\n      \"excludeReason\": \"\"\n    };\n    \n    finalConfig.push(config);\n  });\n}\n\nreturn {\n  success: true,\n  message: \"MCP工具分析完成\",\n  analysis_results: {\n    stage1_structure: stage1,\n    stage2_input_schema: stage2,\n    final_config: finalConfig\n  },\n  final_config: finalConfig\n};"}, "id": "final-integration", "name": "最终整合与生成配置", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "options": {}}, "id": "response-node", "name": "返回分析结果", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1780, 200]}], "connections": {"webhook-start": {"main": [[{"node": "init-data", "type": "main", "index": 0}]]}, "init-data": {"main": [[{"node": "stage1-analysis", "type": "main", "index": 0}]]}, "stage1-analysis": {"main": [[{"node": "parse-stage1", "type": "main", "index": 0}]]}, "parse-stage1": {"main": [[{"node": "stage2-input-schema", "type": "main", "index": 0}]]}, "stage2-input-schema": {"main": [[{"node": "final-integration", "type": "main", "index": 0}]]}, "final-integration": {"main": [[{"node": "response-node", "type": "main", "index": 0}]]}}}