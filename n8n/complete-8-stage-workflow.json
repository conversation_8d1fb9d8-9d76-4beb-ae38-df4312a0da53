{"name": "MCP工具完整8阶段分析工作流", "nodes": [{"parameters": {"httpMethod": "POST", "path": "mcp-8stage-analysis", "responseMode": "responseNode", "options": {}}, "id": "webhook-start", "name": "接收MCP代码", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "webhookId": "mcp-8stage-webhook"}, {"parameters": {"values": {"string": [{"name": "sourceCode", "value": "={{ $json.body.code || $json.body.sourceCode }}"}, {"name": "projectName", "value": "={{ $json.body.projectName || 'MCP工具项目' }}"}]}}, "id": "init-data", "name": "初始化数据", "type": "n8n-nodes-base.set", "typeVersion": 3, "position": [460, 300]}, {"parameters": {"resource": "chat", "model": "gpt-4o", "options": {"temperature": 0.1, "maxTokens": 4000}, "messages": {"messageValues": [{"role": "system", "content": "你是世界级的MCP工具分析专家，专门负责代码理解和结构分析。你具备深厚的编程语言知识和MCP协议理解能力。\n\n你的任务是深入分析给定的MCP工具源代码，提取核心结构信息和功能概览。\n\n分析框架：\n1. 代码架构识别\n2. 工具数量和类型分析\n3. 核心功能逻辑理解\n4. API接口模式识别\n5. 依赖关系初步分析"}, {"role": "user", "content": "请分析以下MCP工具源代码，提取核心结构信息：\n\n代码内容：\n{{ $json.sourceCode }}\n\n请按照以下格式输出分析结果：\n```json\n{\n  \"service_type\": \"single_tool|multi_tool\",\n  \"tool_count\": 数字,\n  \"tools_overview\": [\n    {\n      \"name\": \"工具英文名\",\n      \"chinese_name\": \"生成的中文名\",\n      \"core_function\": \"一句话核心功能\",\n      \"complexity_level\": \"simple|medium|complex\"\n    }\n  ],\n  \"technical_stack\": \"使用的技术栈\",\n  \"architecture_pattern\": \"架构模式\"\n}\n```"}]}}, "id": "stage1-analysis", "name": "阶段1：代码理解与结构分析", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [680, 200]}, {"parameters": {"jsCode": "// 解析阶段1结果\nconst response = $input.item(0).json;\nlet content = '';\n\nif (response.choices && response.choices[0] && response.choices[0].message) {\n  content = response.choices[0].message.content;\n} else if (response.message && response.message.content) {\n  content = response.message.content;\n} else {\n  content = JSON.stringify(response);\n}\n\nlet jsonMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\nif (jsonMatch) {\n  try {\n    const parsedJson = JSON.parse(jsonMatch[1]);\n    return { stage1_result: parsedJson, raw_content: content };\n  } catch (e) {\n    return { stage1_result: content, raw_content: content, parse_error: e.message };\n  }\n} else {\n  return { stage1_result: content, raw_content: content };\n}"}, "id": "parse-stage1", "name": "解析阶段1结果", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 200]}, {"parameters": {"resource": "chat", "model": "gpt-4o", "options": {"temperature": 0.1, "maxTokens": 4000}, "messages": {"messageValues": [{"role": "system", "content": "你是JSON Schema专家和API设计师，专门负责分析和构建标准化的输入参数模式。你对数据类型、验证规则和API设计最佳实践有深入理解。\n\n你的任务是为每个工具生成精确的JSON Schema格式的inputSchema。\n\n分析方法：\n1. 遍历每个工具的函数签名\n2. 分析参数的数据类型和约束\n3. 识别必需参数vs可选参数\n4. 检查参数的验证规则和默认值\n5. 分析复杂对象和嵌套结构"}, {"role": "user", "content": "基于阶段1的分析结果和原始代码，为每个工具生成精确的JSON Schema格式的inputSchema：\n\n阶段1结果：\n{{ JSON.stringify($('parse-stage1').item.json.stage1_result) }}\n\n原始代码：\n{{ $('init-data').item.json.sourceCode }}\n\n请为每个工具生成完整的inputSchema，格式如下：\n```json\n{\n  \"input_schemas\": [\n    {\n      \"tool_name\": \"工具名\",\n      \"schema\": {\n        \"type\": \"object\",\n        \"required\": [\"必需参数数组\"],\n        \"properties\": {\n          \"参数名\": {\n            \"type\": \"数据类型\",\n            \"description\": \"清晰的中文描述\",\n            \"format\": \"格式约束（如适用）\",\n            \"enum\": [\"枚举值（如适用）\"],\n            \"default\": \"默认值（如适用）\",\n            \"minimum\": \"最小值（如适用）\",\n            \"maximum\": \"最大值（如适用）\"\n          }\n        },\n        \"additionalProperties\": false\n      }\n    }\n  ]\n}\n```"}]}}, "id": "stage2-input-schema", "name": "阶段2：输入参数模式提取", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1120, 200]}, {"parameters": {"jsCode": "// 解析阶段2结果\nconst response = $input.item(0).json;\nlet content = '';\n\nif (response.choices && response.choices[0] && response.choices[0].message) {\n  content = response.choices[0].message.content;\n} else if (response.message && response.message.content) {\n  content = response.message.content;\n} else {\n  content = JSON.stringify(response);\n}\n\nlet jsonMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\nif (jsonMatch) {\n  try {\n    const parsedJson = JSON.parse(jsonMatch[1]);\n    return { stage2_result: parsedJson, raw_content: content };\n  } catch (e) {\n    return { stage2_result: content, raw_content: content, parse_error: e.message };\n  }\n} else {\n  return { stage2_result: content, raw_content: content };\n}"}, "id": "parse-stage2", "name": "解析阶段2结果", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 200]}, {"parameters": {"resource": "chat", "model": "gpt-4o", "options": {"temperature": 0.1, "maxTokens": 4000}, "messages": {"messageValues": [{"role": "system", "content": "你是文件系统和数据处理专家，专门分析工具的文件处理能力和数据操作特性。\n\n你的任务是全面分析每个工具的文件处理能力、支持格式和批量处理特性。\n\n分析维度：\n1. 支持的文件格式和扩展名\n2. 文件大小限制和约束\n3. 文件编码和格式要求\n4. 单文件vs多文件处理\n5. 批量处理能力\n6. 目录遍历和处理\n7. 流式处理支持"}, {"role": "user", "content": "基于前面的分析结果，分析每个工具的文件处理能力：\n\n阶段1结果：\n{{ JSON.stringify($('parse-stage1').item.json.stage1_result) }}\n\n阶段2结果：\n{{ JSON.stringify($('parse-stage2').item.json.stage2_result) }}\n\n原始代码：\n{{ $('init-data').item.json.sourceCode }}\n\n请分析文件处理能力，格式如下：\n```json\n{\n  \"file_capabilities\": [\n    {\n      \"tool_name\": \"工具名\",\n      \"supported_extensions\": [\"ext1\", \"ext2\"],\n      \"multi_file_type\": 0,\n      \"can_process_directory\": false,\n      \"file_size_limits\": \"描述文件大小限制\",\n      \"batch_processing\": \"批量处理能力描述\",\n      \"file_parameters\": [\"文件相关参数列表\"]\n    }\n  ]\n}\n```"}]}}, "id": "stage3-file-capability", "name": "阶段3：文件处理能力分析", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1560, 200]}, {"parameters": {"jsCode": "// 解析阶段3结果\nconst response = $input.item(0).json;\nlet content = '';\n\nif (response.choices && response.choices[0] && response.choices[0].message) {\n  content = response.choices[0].message.content;\n} else if (response.message && response.message.content) {\n  content = response.message.content;\n} else {\n  content = JSON.stringify(response);\n}\n\nlet jsonMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\nif (jsonMatch) {\n  try {\n    const parsedJson = JSON.parse(jsonMatch[1]);\n    return { stage3_result: parsedJson, raw_content: content };\n  } catch (e) {\n    return { stage3_result: content, raw_content: content, parse_error: e.message };\n  }\n} else {\n  return { stage3_result: content, raw_content: content };\n}"}, "id": "parse-stage3", "name": "解析阶段3结果", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 200]}, {"parameters": {"resource": "chat", "model": "gpt-4o", "options": {"temperature": 0.1, "maxTokens": 4000}, "messages": {"messageValues": [{"role": "system", "content": "你是跨平台软件开发专家，专门评估工具的平台兼容性和运行环境要求。\n\n你的任务是评估每个工具的平台兼容性、运行环境要求和部署特性。\n\n兼容性矩阵：\n1. Windows兼容性\n2. macOS兼容性\n3. Linux兼容性\n4. 特殊平台要求\n5. 编程语言运行时版本\n6. 系统依赖和库要求\n7. 网络连接需求\n8. 权限和安全要求"}, {"role": "user", "content": "基于前面的分析结果，评估平台兼容性：\n\n技术栈信息：\n{{ JSON.stringify($('parse-stage1').item.json.stage1_result) }}\n\n原始代码：\n{{ $('init-data').item.json.sourceCode }}\n\n请评估平台兼容性，格式如下：\n```json\n{\n  \"platform_compatibility\": [\n    {\n      \"tool_name\": \"工具名\",\n      \"supported_platforms\": [\"windows\", \"macos\", \"linux\"],\n      \"runtime_requirements\": \"运行时要求\",\n      \"system_dependencies\": \"系统依赖\",\n      \"network_requirements\": \"网络要求\",\n      \"permission_level\": \"权限级别\"\n    }\n  ]\n}\n```"}]}}, "id": "stage4-platform-compatibility", "name": "阶段4：平台兼容性评估", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [680, 400]}, {"parameters": {"jsCode": "// 解析阶段4结果\nconst response = $input.item(0).json;\nlet content = '';\n\nif (response.choices && response.choices[0] && response.choices[0].message) {\n  content = response.choices[0].message.content;\n} else if (response.message && response.message.content) {\n  content = response.message.content;\n} else {\n  content = JSON.stringify(response);\n}\n\nlet jsonMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\nif (jsonMatch) {\n  try {\n    const parsedJson = JSON.parse(jsonMatch[1]);\n    return { stage4_result: parsedJson, raw_content: content };\n  } catch (e) {\n    return { stage4_result: content, raw_content: content, parse_error: e.message };\n  }\n} else {\n  return { stage4_result: content, raw_content: content };\n}"}, "id": "parse-stage4", "name": "解析阶段4结果", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 400]}, {"parameters": {"jsCode": "// 整合前4个阶段的结果并生成最终JSON配置\nconst stage1 = $('parse-stage1').item.json.stage1_result;\nconst stage2 = $('parse-stage2').item.json.stage2_result;\nconst stage3 = $('parse-stage3').item.json.stage3_result;\nconst stage4 = $('parse-stage4').item.json.stage4_result;\nconst sourceCode = $('init-data').item.json.sourceCode;\nconst projectName = $('init-data').item.json.projectName;\n\n// 模拟剩余阶段的分析结果\nfunction generateStage5Dependencies(tools) {\n  return {\n    dependency_analysis: tools.map(tool => ({\n      tool_name: tool.name,\n      prerequisite_tools: [],\n      dependency_type: 'optional',\n      dependency_reason: '独立运行'\n    }))\n  };\n}\n\nfunction generateStage6Security(tools) {\n  return {\n    security_assessment: tools.map(tool => ({\n      tool_name: tool.name,\n      risk_level: 'low',\n      can_direct_execute: true,\n      is_dangerous: false,\n      security_concerns: ['输入验证'],\n      mitigation_measures: ['参数校验', '权限控制']\n    }))\n  };\n}\n\nfunction generateStage7Utility(tools) {\n  return {\n    utility_assessment: tools.map(tool => ({\n      tool_name: tool.name,\n      can_run_independently: true,\n      should_exclude: false,\n      exclude_reason: '',\n      utility_score: 8,\n      target_users: ['开发者', '数据分析师'],\n      use_cases: ['自动化任务', '数据处理'],\n      keywords: `${tool.name},MCP工具,自动化`\n    }))\n  };\n}\n\n// 生成最终配置\nfunction generateFinalConfig(stage1, stage2, stage3, stage4, stage5, stage6, stage7, projectName) {\n  if (!stage1.tools_overview || !Array.isArray(stage1.tools_overview)) {\n    return [];\n  }\n  \n  return stage1.tools_overview.map((tool, index) => {\n    // 查找对应的inputSchema\n    let inputSchema = { type: \"object\", properties: {}, required: [] };\n    if (stage2.input_schemas && Array.isArray(stage2.input_schemas)) {\n      const schemaMatch = stage2.input_schemas.find(s => s.tool_name === tool.name);\n      if (schemaMatch && schemaMatch.schema) {\n        inputSchema = schemaMatch.schema;\n      }\n    }\n    \n    // 查找文件处理能力\n    let fileCapability = {};\n    if (stage3.file_capabilities && Array.isArray(stage3.file_capabilities)) {\n      fileCapability = stage3.file_capabilities.find(f => f.tool_name === tool.name) || {};\n    }\n    \n    // 查找安全评估\n    let security = {};\n    if (stage6.security_assessment && Array.isArray(stage6.security_assessment)) {\n      security = stage6.security_assessment.find(s => s.tool_name === tool.name) || {};\n    }\n    \n    // 查找实用性评估\n    let utility = {};\n    if (stage7.utility_assessment && Array.isArray(stage7.utility_assessment)) {\n      utility = stage7.utility_assessment.find(u => u.tool_name === tool.name) || {};\n    }\n\n    return {\n      \"ID\": null,\n      \"c_name\": tool.chinese_name || tool.name,\n      \"description\": `${tool.core_function} - ${tool.name}`,\n      \"descriptionChinese\": `${tool.core_function} - ${tool.chinese_name || tool.name}`,\n      \"fullName\": `${projectName}--${tool.name}`,\n      \"inputSchema\": inputSchema,\n      \"is_single_call\": 1,\n      \"keywords\": utility.keywords || `${tool.name},MCP工具,${tool.core_function}`,\n      \"multiFileType\": fileCapability.multi_file_type || 0,\n      \"name\": tool.name,\n      \"outputSchema\": { \"type\": \"object\" },\n      \"platform\": \"all\",\n      \"points\": 2,\n      \"projectId\": null,\n      \"projectUUId\": \"mcp-tools-project\",\n      \"regex\": null,\n      \"supportedExtensions\": fileCapability.supported_extensions || [],\n      \"canProcessDirectory\": fileCapability.can_process_directory || false,\n      \"canDirectExecute\": security.can_direct_execute !== false,\n      \"isDangerous\": security.is_dangerous || false,\n      \"canRunIndependently\": utility.can_run_independently !== false,\n      \"prerequisiteTools\": [],\n      \"shouldExclude\": utility.should_exclude || false,\n      \"excludeReason\": utility.exclude_reason || \"\"\n    };\n  });\n}\n\n// 执行剩余阶段分析\nconst stage5 = generateStage5Dependencies(stage1.tools_overview || []);\nconst stage6 = generateStage6Security(stage1.tools_overview || []);\nconst stage7 = generateStage7Utility(stage1.tools_overview || []);\nconst finalConfig = generateFinalConfig(stage1, stage2, stage3, stage4, stage5, stage6, stage7, projectName);\n\nreturn {\n  success: true,\n  message: \"MCP工具8阶段分析完成\",\n  analysis_results: {\n    stage1_structure: stage1,\n    stage2_input_schema: stage2,\n    stage3_file_capability: stage3,\n    stage4_platform_compatibility: stage4,\n    stage5_dependency_analysis: stage5,\n    stage6_security_assessment: stage6,\n    stage7_utility_assessment: stage7,\n    stage8_final_config: finalConfig\n  },\n  final_config: finalConfig\n};"}, "id": "stage8-final-integration", "name": "阶段8：最终整合与验证", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "options": {}}, "id": "response-node", "name": "返回分析结果", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1340, 400]}], "connections": {"webhook-start": {"main": [[{"node": "init-data", "type": "main", "index": 0}]]}, "init-data": {"main": [[{"node": "stage1-analysis", "type": "main", "index": 0}]]}, "stage1-analysis": {"main": [[{"node": "parse-stage1", "type": "main", "index": 0}]]}, "parse-stage1": {"main": [[{"node": "stage2-input-schema", "type": "main", "index": 0}]]}, "stage2-input-schema": {"main": [[{"node": "parse-stage2", "type": "main", "index": 0}]]}, "parse-stage2": {"main": [[{"node": "stage3-file-capability", "type": "main", "index": 0}]]}, "stage3-file-capability": {"main": [[{"node": "parse-stage3", "type": "main", "index": 0}]]}, "parse-stage3": {"main": [[{"node": "stage4-platform-compatibility", "type": "main", "index": 0}]]}, "stage4-platform-compatibility": {"main": [[{"node": "parse-stage4", "type": "main", "index": 0}]]}, "parse-stage4": {"main": [[{"node": "stage8-final-integration", "type": "main", "index": 0}]]}, "stage8-final-integration": {"main": [[{"node": "response-node", "type": "main", "index": 0}]]}}}