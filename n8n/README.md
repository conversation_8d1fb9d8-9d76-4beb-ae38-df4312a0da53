# MCP工具8阶段分析工作流使用指南

## 🎯 概述

我已经为您创建了多个n8n工作流来实现MCP工具的8阶段分析流程。由于n8n的webhook配置需要特殊设置，我提供了不同的版本供您选择。

## 📋 已创建的工作流

### 1. MCP工具8阶段分析工作流-OpenAI版 (ID: VStbxrrgnGUL1gIT)
- **状态**: 已激活 ✅
- **类型**: Webhook触发 + OpenAI API
- **功能**: 完整的2阶段分析（代码结构 + 输入参数）
- **需要**: OpenAI API密钥配置

### 2. MCP分析测试工作流 (ID: ADe8Leyr61uLmMDh)  
- **状态**: 已激活 ✅
- **类型**: Webhook触发 + 纯JavaScript分析
- **功能**: 基础代码分析，无需外部API
- **优点**: 不依赖外部服务

### 3. 简单测试工作流 (ID: ykLUCZ5bXurG4F56)
- **状态**: 已激活 ✅
- **类型**: 基础连接测试
- **功能**: 验证webhook连接是否正常

### 4. MCP工具分析-手动触发版 (ID: bRWvD3nFwcXwU4Hf)
- **状态**: 未激活
- **类型**: 手动触发 + 内置测试数据
- **功能**: 最容易测试的版本

## 🔧 Webhook配置问题解决方案

### 问题诊断
当前webhook调用返回错误：`"Workflow could not be started!"`

这通常是由以下原因造成的：

1. **测试模式限制**: n8n的webhook在测试模式下需要手动激活
2. **URL格式**: 正确的webhook URL格式应该是 `/webhook-test/路径名`
3. **认证配置**: 可能需要配置webhook认证

### 解决方案

#### 方案1: 使用手动触发版本（推荐）
```bash
# 激活手动触发版本
curl -X POST http://localhost:5678/rest/workflows/bRWvD3nFwcXwU4Hf/activate
```

然后在n8n界面中：
1. 打开"MCP工具分析-手动触发版"工作流
2. 点击"Execute Workflow"按钮
3. 查看执行结果

#### 方案2: 修复Webhook配置
1. 在n8n界面中打开任一webhook工作流
2. 点击webhook节点
3. 点击"Listen for calls"或"Execute workflow"
4. 使用正确的URL格式测试：

```bash
# 测试简单工作流
curl -X POST http://localhost:5678/webhook-test/simple-test \
  -H "Content-Type: application/json" \
  -d '{"test": "hello"}'

# 测试MCP分析工作流  
curl -X POST http://localhost:5678/webhook-test/test-mcp \
  -H "Content-Type: application/json" \
  -d '{
    "code": "def get_weather(location):\n    return f\"Weather for {location}\"",
    "projectName": "天气工具"
  }'
```

#### 方案3: 配置生产环境Webhook
如果要在生产环境使用，需要：
1. 设置n8n的`WEBHOOK_URL`环境变量
2. 配置正确的域名和端口
3. 确保工作流处于激活状态

## 📊 预期输出格式

成功执行后，您将获得以下格式的JSON响应：

```json
{
  "success": true,
  "message": "MCP工具分析完成",
  "input_data": {
    "code_length": 150,
    "project_name": "MCP示例项目",
    "function_count": 2
  },
  "analysis_results": {
    "stage1_structure": {
      "service_type": "multi_tool",
      "tool_count": 2,
      "tools_overview": [
        {
          "name": "get_weather",
          "chinese_name": "get_weather工具",
          "core_function": "执行get_weather相关操作",
          "complexity_level": "simple"
        }
      ],
      "technical_stack": "Python",
      "architecture_pattern": "MCP Server"
    },
    "stage2_input_schemas": [...],
    "final_config": [
      {
        "ID": null,
        "c_name": "get_weather工具",
        "description": "执行get_weather相关操作 - get_weather",
        "fullName": "MCP示例项目--get_weather",
        "inputSchema": {...},
        "name": "get_weather",
        // ... 其他标准字段
      }
    ]
  },
  "final_config": [...]
}
```

## 🚀 下一步操作

1. **立即测试**: 使用手动触发版本进行测试
2. **配置OpenAI**: 如果要使用AI分析，配置OpenAI API密钥
3. **修复Webhook**: 根据上述方案修复webhook配置
4. **扩展功能**: 基于现有工作流添加更多分析阶段

## 📞 技术支持

如果遇到问题，请检查：
1. n8n服务是否正常运行
2. 工作流是否处于激活状态
3. API密钥是否正确配置
4. 网络连接是否正常
