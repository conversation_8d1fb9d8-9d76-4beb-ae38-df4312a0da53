{"name": "MCP工具分析工作流", "nodes": [{"parameters": {}, "id": "start-node", "name": "开始分析", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "sourceCode", "value": "={{ $json.sourceCode }}"}, {"name": "projectName", "value": "={{ $json.projectName }}"}]}}, "id": "init-data", "name": "初始化数据", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"model": "claude-3-sonnet-20240229", "messages": [{"role": "system", "content": "你是世界级的MCP工具分析专家，专门负责代码理解和结构分析。"}, {"role": "user", "content": "请分析以下MCP工具源代码的结构：\n\n{{ $json.sourceCode }}\n\n请按照以下格式输出分析结果：\n<code_analysis>\n<service_type>[single_tool|multi_tool]</service_type>\n<tool_count>[数字]</tool_count>\n<tools_overview>\n  <tool>\n    <name>[工具英文名]</name>\n    <chinese_name>[生成的中文名]</chinese_name>\n    <core_function>[一句话核心功能]</core_function>\n    <complexity_level>[simple|medium|complex]</complexity_level>\n  </tool>\n</tools_overview>\n<technical_stack>[使用的技术栈]</technical_stack>\n<architecture_pattern>[架构模式]</architecture_pattern>\n</code_analysis>"}]}, "id": "stage1-analysis", "name": "阶段1：代码理解与结构分析", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [680, 200]}, {"parameters": {"model": "claude-3-sonnet-20240229", "messages": [{"role": "system", "content": "你是JSON Schema专家和API设计师，专门负责分析和构建标准化的输入参数模式。"}, {"role": "user", "content": "基于以下代码分析结果，为每个工具生成精确的JSON Schema格式的inputSchema：\n\n代码：{{ $('init-data').item.json.sourceCode }}\n\n结构分析：{{ $json.message.content }}\n\n请输出标准的JSON Schema格式。"}]}, "id": "stage2-schema", "name": "阶段2：输入参数模式提取", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [900, 200]}, {"parameters": {"model": "claude-3-sonnet-20240229", "messages": [{"role": "system", "content": "你是文件系统和数据处理专家，专门分析工具的文件处理能力和数据操作特性。"}, {"role": "user", "content": "分析工具的文件处理能力：\n\n代码：{{ $('init-data').item.json.sourceCode }}\n\nSchema：{{ $json.message.content }}\n\n请分析支持的文件格式、批量处理能力等。"}]}, "id": "stage3-files", "name": "阶段3：文件处理能力分析", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1120, 200]}, {"parameters": {"model": "claude-3-sonnet-20240229", "messages": [{"role": "system", "content": "你是跨平台软件开发专家，专门评估工具的平台兼容性和运行环境要求。"}, {"role": "user", "content": "评估平台兼容性：\n\n代码：{{ $('init-data').item.json.sourceCode }}\n\n请分析支持的操作系统、运行时要求、系统依赖等。"}]}, "id": "stage4-platform", "name": "阶段4：平台兼容性评估", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [680, 400]}, {"parameters": {"model": "claude-3-sonnet-20240229", "messages": [{"role": "system", "content": "你是工作流设计专家和依赖关系分析师，专门分析工具间的依赖关系和执行顺序。"}, {"role": "user", "content": "分析前置依赖关系：\n\n代码：{{ $('init-data').item.json.sourceCode }}\n\n结构分析：{{ $('stage1-analysis').item.json.message.content }}\n\n请分析工具间的依赖关系和执行顺序。"}]}, "id": "stage5-dependencies", "name": "阶段5：前置依赖关系分析", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"model": "claude-3-sonnet-20240229", "messages": [{"role": "system", "content": "你是网络安全专家和风险评估师，专门评估软件工具的安全风险和潜在威胁。"}, {"role": "user", "content": "评估安全性与风险：\n\n代码：{{ $('init-data').item.json.sourceCode }}\n\n请评估安全风险、潜在威胁和安全最佳实践。"}]}, "id": "stage6-security", "name": "阶段6：安全性与风险评估", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1120, 400]}, {"parameters": {"model": "claude-3-sonnet-20240229", "messages": [{"role": "system", "content": "你是产品经理和用户体验专家，专门评估工具的实用性、用户价值和市场定位。"}, {"role": "user", "content": "评估实用性与检索优化：\n\n代码：{{ $('init-data').item.json.sourceCode }}\n\n请评估工具的实用性价值，生成优化的检索关键词和用户场景。"}]}, "id": "stage7-utility", "name": "阶段7：实用性与检索优化", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [680, 600]}, {"parameters": {"model": "claude-3-sonnet-20240229", "messages": [{"role": "system", "content": "你是质量保证专家和数据整合师，负责将所有分析结果整合为最终的标准化JSON格式。"}, {"role": "user", "content": "整合所有分析结果生成最终JSON配置：\n\n阶段1：{{ $('stage1-analysis').item.json.message.content }}\n阶段2：{{ $('stage2-schema').item.json.message.content }}\n阶段3：{{ $('stage3-files').item.json.message.content }}\n阶段4：{{ $('stage4-platform').item.json.message.content }}\n阶段5：{{ $('stage5-dependencies').item.json.message.content }}\n阶段6：{{ $('stage6-security').item.json.message.content }}\n阶段7：{{ $('stage7-utility').item.json.message.content }}\n\n请生成完整的标准化JSON配置。"}]}, "id": "stage8-integration", "name": "阶段8：最终整合与验证", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [900, 600]}, {"parameters": {"filePath": "/tmp/mcp-analysis-result.json", "fileContent": "={{ $json.message.content }}"}, "id": "save-result", "name": "保存分析结果", "type": "n8n-nodes-base.writeFile", "typeVersion": 1, "position": [1120, 600]}], "connections": {"开始分析": {"main": [[{"node": "初始化数据", "type": "main", "index": 0}]]}, "初始化数据": {"main": [[{"node": "阶段1：代码理解与结构分析", "type": "main", "index": 0}]]}, "阶段1：代码理解与结构分析": {"main": [[{"node": "阶段2：输入参数模式提取", "type": "main", "index": 0}, {"node": "阶段4：平台兼容性评估", "type": "main", "index": 0}]]}, "阶段2：输入参数模式提取": {"main": [[{"node": "阶段3：文件处理能力分析", "type": "main", "index": 0}, {"node": "阶段5：前置依赖关系分析", "type": "main", "index": 0}]]}, "阶段3：文件处理能力分析": {"main": [[{"node": "阶段6：安全性与风险评估", "type": "main", "index": 0}]]}, "阶段4：平台兼容性评估": {"main": [[{"node": "阶段7：实用性与检索优化", "type": "main", "index": 0}]]}, "阶段5：前置依赖关系分析": {"main": [[{"node": "阶段8：最终整合与验证", "type": "main", "index": 0}]]}, "阶段6：安全性与风险评估": {"main": [[{"node": "阶段8：最终整合与验证", "type": "main", "index": 0}]]}, "阶段7：实用性与检索优化": {"main": [[{"node": "阶段8：最终整合与验证", "type": "main", "index": 0}]]}, "阶段8：最终整合与验证": {"main": [[{"node": "保存分析结果", "type": "main", "index": 0}]]}}}